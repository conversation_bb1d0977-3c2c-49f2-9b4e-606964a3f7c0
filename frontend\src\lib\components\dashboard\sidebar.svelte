<script lang="ts">
	export let server: {
		id: string;
		name: string;
		icon: string | null;
		owner: boolean;
		permissions: string;
	};
	export let selected_category: string;
	export let on_category_change: (category: string) => void;

	const categories = [
		{
			key: 'customization',
			name: 'Customization',
			icon: 'fas fa-palette',
			description: 'Customize your league appearance'
		},
		{
			key: 'management',
			name: 'League Management',
			icon: 'fas fa-users-cog',
			description: 'Staff roles and channels'
		},
		{
			key: 'season',
			name: 'Season',
			icon: 'fas fa-calendar-alt',
			description: 'Season related options'
		},
		{
			key: 'franchise',
			name: 'Franchise',
			icon: 'fas fa-building',
			description: 'Team owner management'
		},
		{
			key: 'roster',
			name: 'Roster',
			icon: 'fas fa-users',
			description: 'Roster management settings'
		},
		{
			key: 'suspensions',
			name: 'Suspensions',
			icon: 'fas fa-ban',
			description: 'Suspension settings'
		},
		{
			key: 'transactions',
			name: 'Transactions',
			icon: 'fas fa-exchange-alt',
			description: 'Team transactions'
		},
		{
			key: 'transaction_extras',
			name: 'Transaction Extras',
			icon: 'fas fa-plus-circle',
			description: 'Extra transaction settings'
		},
		{
			key: 'demands',
			name: 'Demands',
			icon: 'fas fa-exclamation-circle',
			description: 'Player demand settings'
		},
		{
			key: 'pickups',
			name: 'Pickups',
			icon: 'fas fa-basketball-ball',
			description: 'Pickup game settings'
		},
		{
			key: 'reset',
			name: 'Reset',
			icon: 'fas fa-undo',
			description: 'Reset settings'
		}
	];

	function get_server_icon_url(server_id: string, icon_hash: string | null) {
		if (!icon_hash) {
			return `https://cdn.discordapp.com/embed/avatars/${parseInt(server_id) % 5}.png`;
		}
		return `https://cdn.discordapp.com/icons/${server_id}/${icon_hash}.png?size=64`;
	}

	function handle_category_click(category_key: string) {
		on_category_change(category_key);
	}
</script>

<aside class="fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 bg-gray-900 border-r border-gray-700 overflow-y-auto">
	<div class="p-4 border-b border-gray-700">
		<div class="flex items-center space-x-3">
			<img 
				src={get_server_icon_url(server.id, server.icon)} 
				alt="{server.name} icon"
				class="w-12 h-12 rounded-full border-2 border-gray-600"
			>
			<div class="flex-1 min-w-0">
				<h2 class="text-lg font-semibold text-white truncate">{server.name}</h2>
				<p class="text-sm text-gray-400">Dashboard</p>
			</div>
		</div>
	</div>

	<nav class="p-4">
		<div class="space-y-2">
			{#each categories as category}
				<button
					onclick={() => handle_category_click(category.key)}
					class="w-full flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 text-left group {selected_category === category.key ? 'bg-pink-500/20 text-pink-400 border border-pink-500/30' : 'text-gray-300 hover:bg-gray-800 hover:text-white'}"
				>
					<i class="{category.icon} w-5 text-center {selected_category === category.key ? 'text-pink-400' : 'text-gray-400 group-hover:text-pink-400'}"></i>
					<div class="flex-1 min-w-0">
						<div class="font-medium truncate">{category.name}</div>
						<div class="text-xs {selected_category === category.key ? 'text-pink-300' : 'text-gray-500'} truncate">
							{category.description}
						</div>
					</div>
					{#if selected_category === category.key}
						<i class="fas fa-chevron-right text-pink-400 text-sm"></i>
					{/if}
				</button>
			{/each}
		</div>
	</nav>

	<div class="p-4 border-t border-gray-700 mt-auto">
		<div class="bg-gray-800 rounded-lg p-3">
			<div class="flex items-center space-x-2 mb-2">
				<i class="fas fa-info-circle text-blue-400"></i>
				<span class="text-sm font-medium text-white">Quick Tip</span>
			</div>
			<p class="text-xs text-gray-400">
				Changes are saved automatically. Make sure you have the proper permissions to modify these settings.
			</p>
		</div>
	</div>
</aside>
