<script lang="ts">
	const features = [
		{
			icon: 'fas fa-users',
			title: 'Team Management',
			description: 'Effortlessly organize teams, assign coaches, and manage player rosters with intuitive commands.'
		},
		{
			icon: 'fas fa-calendar-alt',
			title: 'Season Planning',
			description: 'Create comprehensive season schedules, track standings, and automate league progression.'
		},
		{
			icon: 'fas fa-chart-line',
			title: 'Advanced Analytics',
			description: 'Deep insights into player performance, team statistics, and league-wide trends.'
		},
		{
			icon: 'fas fa-trophy',
			title: 'Tournament System',
			description: 'Run knockout tournaments, round-robin leagues, and custom competition formats.'
		},
		{
			icon: 'fas fa-cog',
			title: 'Customization',
			description: 'Tailor the bot to your league with custom commands, roles, and automated workflows.'
		},
		{
			icon: 'fas fa-shield-alt',
			title: 'Moderation Tools',
			description: 'Built-in moderation features to maintain fair play and community standards.'
		}
	];
</script>

<section id="features" class="py-20 px-4 bg-gray-800">
	<div class="max-w-6xl mx-auto">
		<div class="text-center mb-16">
			<h2 class="text-4xl md:text-5xl font-bold text-white mb-4">
				Why Choose <span class="text-pink-400">Peerless</span>?
			</h2>
			<p class="text-xl text-gray-300 max-w-3xl mx-auto">
				Built specifically for Roblox sports leagues, Peerless offers everything you need to run professional competitions
			</p>
		</div>
		
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
			{#each features as feature, index}
				<div class="bg-gray-900 rounded-xl p-8 border border-gray-700 hover:border-pink-500 transition-all duration-300 transform hover:scale-105 animate-fade-in-up" 
					 style="animation-delay: {index * 100}ms;">
					<div class="text-pink-400 text-4xl mb-4">
						<i class={feature.icon}></i>
					</div>
					<h3 class="text-xl font-bold text-white mb-3">{feature.title}</h3>
					<p class="text-gray-300 leading-relaxed">{feature.description}</p>
				</div>
			{/each}
		</div>
	</div>
</section>
