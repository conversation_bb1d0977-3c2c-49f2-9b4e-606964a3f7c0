<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';

    let show_dropdown = $state(false);
    let menu_open = $state(false);
    let mobile_menu: HTMLElement;

    let authenticated = $state(false);
    let user = $state({
        username: '',
        discord_id: '',
        avatar_url: ''
    });

    async function store_user_data(user_data) {
        try {
            await fetch('/account/user', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(user_data)
            });
            localStorage.setItem('user', JSON.stringify(user_data));
        } catch (e) {
            console.error('Failed to store user data:', e);
            localStorage.setItem('user', JSON.stringify(user_data));
        }
    }

    async function load_user_data(discord_id) {
        try {
            const response = await fetch(`/account/user/${discord_id}`);
            if (response.ok) {
                const user_data = await response.json();
                if (!user_data.error) {
                    return user_data;
                }
            }
        } catch (e) {
            console.error('Failed to load user data from API:', e);
        }
        return null;
    }

    onMount(async () => {
        if (browser) {
            const saved_user = localStorage.getItem('user');
            if (saved_user) {
                try {
                    const local_user = JSON.parse(saved_user);
                    const api_user = await load_user_data(local_user.discord_id);

                    if (api_user) {
                        user = api_user;
                        authenticated = true;
                        localStorage.setItem('user', JSON.stringify(api_user));
                    } else {
                        user = local_user;
                        authenticated = true;
                    }
                } catch (e) {
                    localStorage.removeItem('user');
                }
            }

            const url_params = new URLSearchParams(window.location.search);
            const username = url_params.get('username');
            const discord_id = url_params.get('discord_id');
            const avatar_url = url_params.get('avatar_url');

            if (username && discord_id && avatar_url) {
                const user_data = {
                    username: decodeURIComponent(username),
                    discord_id: discord_id,
                    avatar_url: decodeURIComponent(avatar_url)
                };

                user = user_data;
                authenticated = true;

                await store_user_data(user_data);

                const new_url = window.location.pathname;
                window.history.replaceState({}, '', new_url);
            }
        }
    });

    function toggle_mobile_menu() {
        menu_open = !menu_open;
        mobile_menu.classList.toggle('hidden');
    }

    function toggle_dropdown() {
        show_dropdown = !show_dropdown;
    }

    function handle_logout() {
        if (browser) {
            localStorage.removeItem('user');
        }
        authenticated = false;
        user = {
            username: '',
            discord_id: '',
            avatar_url: ''
        };
    }
</script>

<header class="sticky top-0 z-50 bg-gray-800/95 backdrop-blur-sm shadow-md">
    <nav class="p-4 flex items-center justify-between">
        <div class="flex items-center space-x-6">
            <a href="/" class="flex items-center hover:text-pink-400 transition-colors" aria-label="Peerless Home">
                <img src="pink.png" alt="Peerless Logo" class="h-10 mr-3 filter drop-shadow-lg">
                <span class="text-xl font-bold">Peerless</span>
            </a>

            <div class="hidden md:flex items-center space-x-6">
                <a href="#features" class="text-xl text-gray-300 hover:text-pink-400 transition-colors">Features</a>
                <a href="#pricing" class="text-xl text-gray-300 hover:text-pink-400 transition-colors">Pricing</a>
                <a href="#support" class="text-xl text-gray-300 hover:text-pink-400 transition-colors">Support</a>
                <a href="#status" class="text-xl text-gray-300 hover:text-pink-400 transition-colors">Status</a>
                <a href="https://discord.com/api/oauth2/authorize?client_id=1105640024113954829&permissions=9193109515280&scope=bot+applications.commands" class="text-xl text-gray-300 hover:text-pink-400 transition-colors">Invite</a>
            </div>
        </div>

        <div class="relative flex items-center justify-center">
            {#if authenticated}
                <div>
                    <button
                        onclick={toggle_dropdown}
                        class="flex items-center space-x-2 hover:text-pink-400 text-white px-4 rounded-lg transition-colors cursor-pointer focus:outline-none"
                    >
                        <span class="hidden md:block">{user.username}</span>
                        <img src={user.avatar_url} alt="Profile" class="h-10 w-10 rounded-full filter drop-shadow-lg" />
                        <i
                            class="fas fa-chevron-down text-gray-300 text-sm transition-transform duration-200"
                            class:rotate-180={show_dropdown}
                        ></i>
                    </button>

                    {#if show_dropdown}
                        <!-- Dropdown Menu -->
                        <div
                            class="absolute right-0 top-12 mt-1 w-48 bg-gray-800 text-white shadow-lg border-2 border-gray-700"
                        >
                            <a href="/servers" class="block pl-0.5 py-2 hover:bg-gray-700 transition-colors">
                                <div class="flex items-center space-x-2 text-right">
                                    <i class="fas fa-server w-6"></i>
                                    <p>Servers</p>
                                </div>
                            </a>
                            <a href="/custom-bots" class="block pl-0.5 py-2 hover:bg-gray-700 transition-colors">
                                <div class="flex items-center space-x-2 text-right">
                                    <i class="fas fa-cogs w-6"></i>
                                    <p>Custom Bots</p>
                                </div>
                            </a>
                            <button onclick={handle_logout} class="block pl-0.5 py-2 hover:bg-red-500 transition-colors w-full text-left">
                                <div class="flex items-center space-x-2 text-right">
                                    <i class="fas fa-sign-out-alt w-6"></i>
                                    <p>Log Out</p>
                                </div>
                            </button>
                        </div>
                    {/if}
                </div>
            {:else}
                <a
                    href="/account/login/discord"
                    class="flex items-center space-x-2 bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg transition-colors cursor-pointer"
                >
                    <i class="fab fa-discord"></i>
                    <span>Login</span>
                </a>
            {/if}

            <div class="relative flex items-center justify-center">
                <button
                    id="mobileMenuBtn"
                    onclick={toggle_mobile_menu}
                    aria-label="Toggle mobile menu"
                    class="md:hidden ml-4 text-gray-300 hover:text-pink-400 focus:outline-none flex items-center transition-transform duration-300"
                >
                    {#if menu_open}
                        <i class="fas fa-times text-2xl transition-transform duration-300"></i>
                    {:else}
                        <i class="fas fa-bars text-2xl transition-transform duration-300"></i>
                    {/if}
                </button>
            </div>
        </div>
    </nav>

    <div bind:this={mobile_menu} class="md:hidden hidden">
        <div class="flex flex-col items-center border-b-2 border-gray-700 px-4 text-center">
            <div class="border-2 border-b-0 border-gray-700 rounded-t-lg flex flex-col w-full overflow-hidden">
                <a href="#features" class="w-full py-0.5 bg-gray-900 text-2xl font-medium text-gray-300 hover:text-pink-400 transition-colors">Features</a>
                <a href="#pricing" class="w-full py-0.5 bg-gray-800 text-2xl font-medium text-gray-300 hover:text-pink-400 transition-colors">Pricing</a>
                <a href="#support" class="w-full py-0.5 bg-gray-900 text-2xl font-medium text-gray-300 hover:text-pink-400 transition-colors">Support</a>
                <a href="#status" class="w-full py-0.5 bg-gray-800 text-2xl font-medium text-gray-300 hover:text-pink-400 transition-colors">Status</a>
                <a 
                    href="https://discord.com/api/oauth2/authorize?client_id=1105640024113954829&permissions=9193109515280&scope=bot+applications.commands" 
                    class="w-full py-0.5 bg-gray-900 text-2xl font-medium text-gray-300 hover:text-pink-400 transition-colors"
                >Invite</a>
            </div>
        </div>
    </div>
</header>