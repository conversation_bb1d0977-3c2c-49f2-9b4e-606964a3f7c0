from litestar import Controller, get, post
from litestar.response import Response
from litestar.status_codes import HTTP_200_OK, HTTP_400_BAD_REQUEST, HTTP_401_UNAUTHORIZED, HTTP_404_NOT_FOUND, HTTP_500_INTERNAL_SERVER_ERROR
from typing import Dict, Any
import json
import os
import redis
import asyncio
import aiohttp

class ServersController(Controller):
    path = "/api/servers"

    @property
    def redis_client(self):
        redis_host = os.getenv('REDIS_HOST', 'localhost')
        redis_port = int(os.getenv('REDIS_PORT', 6379))
        print(f"Connecting to Redis at {redis_host}:{redis_port}")
        return redis.Redis(
            host=redis_host,
            port=redis_port,
            password=os.getenv('REDIS_PASSWORD'),
            decode_responses=True
        )

    @get("/mutual/{user_id:str}")
    async def get_mutual_servers(self, user_id: str) -> Response:
        try:
            cached_servers = self.redis_client.get(f"mutual_servers:{user_id}")
            if cached_servers:
                servers_data = json.loads(cached_servers)
                return Response(
                    content={"servers": servers_data},
                    status_code=HTTP_200_OK
                )

            # Get user's access token from Redis (stored during OAuth)
            user_token = self.redis_client.get(f"user_token:{user_id}")
            if not user_token:
                return Response(
                    content={"error": "User not authenticated or token expired"},
                    status_code=HTTP_401_UNAUTHORIZED
                )

            bot_token = os.getenv('TOKEN')
            if not bot_token:
                return Response(
                    content={"error": "Bot token not configured"},
                    status_code=HTTP_500_INTERNAL_SERVER_ERROR
                )

            async with aiohttp.ClientSession() as session:
                # Get user guilds using user's access token
                user_headers = {"Authorization": f"Bearer {user_token}"}
                async with session.get("https://discord.com/api/v10/users/@me/guilds", headers=user_headers) as resp:
                    if resp.status != 200:
                        return Response(
                            content={"error": "Failed to fetch user guilds"},
                            status_code=HTTP_400_BAD_REQUEST
                        )
                    user_guilds = await resp.json()

                # Get bot guilds using bot token
                bot_headers = {"Authorization": f"Bot {bot_token}"}
                async with session.get("https://discord.com/api/v10/users/@me/guilds", headers=bot_headers) as resp:
                    if resp.status != 200:
                        return Response(
                            content={"error": "Failed to fetch bot guilds"},
                            status_code=HTTP_400_BAD_REQUEST
                        )
                    bot_guilds = await resp.json()

            bot_guild_ids = {guild['id'] for guild in bot_guilds}
            mutual_servers = []

            for guild in user_guilds:
                if guild['id'] in bot_guild_ids:
                    permissions = int(guild.get('permissions', '0'))
                    if (permissions & 0x20) or (permissions & 0x8):
                        mutual_servers.append({
                            'id': guild['id'],
                            'name': guild['name'],
                            'icon': guild.get('icon'),
                            'owner': guild.get('owner', False),
                            'permissions': guild.get('permissions', '0'),
                            'member_count': guild.get('approximate_member_count'),
                            'bot_permissions': ['Manage Server', 'Send Messages', 'Embed Links']
                        })

            self.redis_client.setex(f"mutual_servers:{user_id}", 300, json.dumps(mutual_servers))

            return Response(
                content={"servers": mutual_servers},
                status_code=HTTP_200_OK
            )

        except Exception as e:
            return Response(
                content={"error": f"Internal server error: {str(e)}"},
                status_code=HTTP_500_INTERNAL_SERVER_ERROR
            )

    @get("/{server_id:str}")
    async def get_server_info(self, server_id: str) -> Response:
        try:
            cached_server = self.redis_client.get(f"server_info:{server_id}")
            if cached_server:
                server_data = json.loads(cached_server)
                return Response(content=server_data, status_code=HTTP_200_OK)

            bot_token = os.getenv('TOKEN')
            if not bot_token:
                return Response(
                    content={"error": "Bot token not configured"},
                    status_code=HTTP_500_INTERNAL_SERVER_ERROR
                )

            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bot {bot_token}"}
                
                async with session.get(f"https://discord.com/api/v10/guilds/{server_id}", headers=headers) as resp:
                    if resp.status == 404:
                        return Response(
                            content={"error": "Server not found or bot not in server"},
                            status_code=HTTP_404_NOT_FOUND
                        )
                    elif resp.status != 200:
                        return Response(
                            content={"error": "Failed to fetch server info"},
                            status_code=HTTP_400_BAD_REQUEST
                        )
                    
                    server_data = await resp.json()

            server_info = {
                'id': server_data['id'],
                'name': server_data['name'],
                'icon': server_data.get('icon'),
                'owner': False,
                'permissions': '0',
                'member_count': server_data.get('approximate_member_count'),
                'description': server_data.get('description')
            }

            self.redis_client.setex(f"server_info:{server_id}", 300, json.dumps(server_info))

            return Response(content=server_info, status_code=HTTP_200_OK)

        except Exception as e:
            return Response(
                content={"error": f"Internal server error: {str(e)}"},
                status_code=HTTP_500_INTERNAL_SERVER_ERROR
            )

    @get("/{server_id:str}/settings")
    async def get_server_settings(self, server_id: str) -> Response:
        try:
            settings_key = f"server_settings:{server_id}"
            cached_settings = self.redis_client.get(settings_key)
            
            if cached_settings:
                settings_data = json.loads(cached_settings)
            else:
                settings_data = {}

            return Response(
                content={"settings": settings_data},
                status_code=HTTP_200_OK
            )

        except Exception as e:
            return Response(
                content={"error": f"Internal server error: {str(e)}"},
                status_code=HTTP_500_INTERNAL_SERVER_ERROR
            )

    @post("/{server_id:str}/settings")
    async def save_server_setting(self, server_id: str, data: Dict[str, Any]) -> Response:
        try:
            category = data.get('category')
            setting = data.get('setting')
            value = data.get('value')
            user_id = data.get('user_id')

            if not all([category, setting, user_id]):
                return Response(
                    content={"error": "Missing required fields"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            settings_key = f"server_settings:{server_id}"
            cached_settings = self.redis_client.get(settings_key)
            
            if cached_settings:
                settings_data = json.loads(cached_settings)
            else:
                settings_data = {}

            if category not in settings_data:
                settings_data[category] = {}

            settings_data[category][setting] = value

            self.redis_client.setex(settings_key, 86400, json.dumps(settings_data))

            log_entry = {
                'server_id': server_id,
                'user_id': user_id,
                'category': category,
                'setting': setting,
                'value': value,
                'timestamp': str(asyncio.get_event_loop().time())
            }
            
            log_key = f"setting_changes:{server_id}"
            self.redis_client.lpush(log_key, json.dumps(log_entry))
            self.redis_client.ltrim(log_key, 0, 99)
            self.redis_client.expire(log_key, 86400)

            return Response(
                content={"success": True, "message": "Setting saved successfully"},
                status_code=HTTP_200_OK
            )

        except Exception as e:
            return Response(
                content={"error": f"Internal server error: {str(e)}"},
                status_code=HTTP_500_INTERNAL_SERVER_ERROR
            )
