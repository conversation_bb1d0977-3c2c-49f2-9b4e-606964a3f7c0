<script lang="ts">
	import { onMount } from 'svelte';
	import EnhancedSettingInput from './enhanced_setting_input.svelte';

	let { server, server_settings, save_setting } = $props();

	let settings_config = $state([]);
	let saving_states = $state({});
	let server_data = $state({
		channels: [],
		roles: [],
		members: []
	});

	onMount(async () => {
		try {
			const response = await fetch('/settings.json');
			if (response.ok) {
				settings_config = await response.json();
			}
		} catch (e) {
			console.error('Failed to load settings config:', e);
		}

		if (server?.id) {
			try {
				const response = await fetch(`/api/servers/${server.id}/data`);
				if (response.ok) {
					server_data = await response.json();
				}
			} catch (e) {
				console.error('Failed to load server data:', e);
			}
		}
	});

	async function handle_setting_change(category_key: string, setting_key: string, value: any) {
		const save_key = `${category_key}_${setting_key}`;
		saving_states[save_key] = true;
		saving_states = { ...saving_states };

		try {
			const result = await save_setting(category_key, setting_key, value);
			if (result.success) {
				saving_states[save_key] = false;
			} else {
				saving_states[save_key] = 'error';
				console.error('Failed to save setting:', result.error);
			}
		} catch (e) {
			saving_states[save_key] = 'error';
			console.error('Error saving setting:', e);
		}

		saving_states = { ...saving_states };

		setTimeout(() => {
			if (saving_states[save_key] === 'error') {
				saving_states[save_key] = false;
				saving_states = { ...saving_states };
			}
		}, 3000);
	}

	function get_setting_value(category_key: string, setting_key: string) {
		const stored_value = server_settings[category_key]?.[setting_key];
		if (stored_value !== undefined && stored_value !== null) {
			return stored_value;
		}

		const category = settings_config.find(cat => cat.key === category_key);
		const setting = category?.settings?.find(s => s.key === setting_key);
		return setting?.default_value ?? null;
	}

	function get_saving_state(category_key: string, setting_key: string) {
		return saving_states[`${category_key}_${setting_key}`] || false;
	}
</script>

<div class="p-8">
	{#if settings_config.length > 0}
		<div class="space-y-12">
			{#each settings_config as category}
				<section id="category-{category.key}" class="scroll-mt-20">
					<div class="mb-8">
						<h1 class="text-3xl font-bold text-white mb-2 flex items-center">
							<i class="{category.icon} text-pink-400 mr-3"></i>
							{category.name}
						</h1>
						<p class="text-gray-300">{category.description}</p>
					</div>

					{#if category.settings && category.settings.length > 0}
						<div class="space-y-6">
							{#each category.settings as setting}
								<EnhancedSettingInput
									{setting}
									value={get_setting_value(category.key, setting.key)}
									on_change={(value: any) => handle_setting_change(category.key, setting.key, value)}
									disabled={get_saving_state(category.key, setting.key) === true}
									{server_data}
									saving_state={get_saving_state(category.key, setting.key)}
								/>
							{/each}
						</div>
					{:else}
						<div class="text-center py-12">
							<div class="bg-gray-900 rounded-xl p-6 max-w-md mx-auto border border-gray-700">
								<i class="fas fa-cog text-gray-400 text-3xl mb-3"></i>
								<h3 class="text-lg font-semibold text-white mb-2">No Settings Available</h3>
								<p class="text-gray-300 text-sm">This category doesn't have any configurable settings yet.</p>
							</div>
						</div>
					{/if}
				</section>
			{/each}
		</div>
	{:else}
		<div class="text-center py-20">
			<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-400 mx-auto mb-4"></div>
				<h2 class="text-xl font-semibold text-white">Loading settings...</h2>
			</div>
		</div>
	{/if}
</div>
