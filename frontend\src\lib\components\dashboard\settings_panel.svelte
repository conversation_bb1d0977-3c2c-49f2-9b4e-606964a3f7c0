<script lang="ts">
	import { onMount } from 'svelte';
	import EnhancedSettingInput from './enhanced_setting_input.svelte';

	let { server, selected_category, server_settings, save_setting } = $props();

	let settings_config = $state([]);
	let current_category = $state(null);
	let saving_states = $state({});
	let server_data = $state({
		channels: [],
		roles: [],
		members: []
	});

	onMount(async () => {
		try {
			const response = await fetch('/settings.json');
			if (response.ok) {
				settings_config = await response.json();
			}
		} catch (e) {
			console.error('Failed to load settings config:', e);
		}

		if (server?.id) {
			try {
				const response = await fetch(`/api/servers/${server.id}/data`);
				if (response.ok) {
					server_data = await response.json();
				}
			} catch (e) {
				console.error('Failed to load server data:', e);
			}
		}
	});

	$effect(() => {
		current_category = settings_config.find(cat => cat.key === selected_category);
	});

	async function handle_setting_change(setting_key: string, value: any) {
		const save_key = `${selected_category}_${setting_key}`;
		saving_states[save_key] = true;
		saving_states = { ...saving_states };

		try {
			const result = await save_setting(selected_category, setting_key, value);
			if (result.success) {
				saving_states[save_key] = false;
			} else {
				saving_states[save_key] = 'error';
				console.error('Failed to save setting:', result.error);
			}
		} catch (e) {
			saving_states[save_key] = 'error';
			console.error('Error saving setting:', e);
		}

		saving_states = { ...saving_states };
		
		setTimeout(() => {
			if (saving_states[save_key] === 'error') {
				saving_states[save_key] = false;
				saving_states = { ...saving_states };
			}
		}, 3000);
	}

	function get_setting_value(setting_key: string) {
		return server_settings[selected_category]?.[setting_key] ?? null;
	}

	function get_saving_state(setting_key: string) {
		return saving_states[`${selected_category}_${setting_key}`] || false;
	}
</script>

<div class="p-8">
	{#if current_category}
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-white mb-2">{current_category.name}</h1>
			<p class="text-gray-300">{current_category.description}</p>
		</div>

		{#if current_category.settings && current_category.settings.length > 0}
			<div class="space-y-6">
				{#each current_category.settings as setting}
					<div class="bg-gray-900 rounded-xl border border-gray-700 p-6">
						<div class="flex items-start justify-between mb-4">
							<div class="flex-1">
								<div class="flex items-center space-x-2 mb-2">
									{#if setting.icon}
										<i class="{setting.icon} text-pink-400"></i>
									{/if}
									<h3 class="text-lg font-semibold text-white">{setting.name}</h3>
									{#if setting.required}
										<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500/20 text-red-400 border border-red-500/30">
											Required
										</span>
									{/if}
								</div>
								<p class="text-gray-300 text-sm">{setting.description}</p>
							</div>
							
							<div class="ml-4 flex items-center space-x-2">
								{#if get_saving_state(setting.key) === true}
									<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-pink-400"></div>
									<span class="text-sm text-gray-400">Saving...</span>
								{:else if get_saving_state(setting.key) === 'error'}
									<i class="fas fa-exclamation-triangle text-red-400"></i>
									<span class="text-sm text-red-400">Error</span>
								{:else if get_setting_value(setting.key) !== null}
									<i class="fas fa-check text-green-400"></i>
									<span class="text-sm text-green-400">Saved</span>
								{/if}
							</div>
						</div>

						<EnhancedSettingInput
							{setting}
							value={get_setting_value(setting.key)}
							on_change={(value: any) => handle_setting_change(setting.key, value)}
							disabled={get_saving_state(setting.key) === true}
							{server_data}
						/>
					</div>
				{/each}
			</div>
		{:else}
			<div class="text-center py-20">
				<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700">
					<i class="fas fa-cog text-gray-400 text-4xl mb-4"></i>
					<h2 class="text-2xl font-bold text-white mb-4">No Settings Available</h2>
					<p class="text-gray-300">This category doesn't have any configurable settings yet.</p>
				</div>
			</div>
		{/if}
	{:else}
		<div class="text-center py-20">
			<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-400 mx-auto mb-4"></div>
				<h2 class="text-xl font-semibold text-white">Loading settings...</h2>
			</div>
		</div>
	{/if}
</div>
