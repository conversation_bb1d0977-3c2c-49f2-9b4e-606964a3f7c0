<script lang="ts">
	import { onMount } from 'svelte';

	let logo_loaded = false;

	onMount(() => {
		logo_loaded = true;
	});
</script>

<section class="relative min-h-screen flex items-center justify-center px-4 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
	<div class="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-purple-500/10"></div>
	
	<div class="relative z-10 text-center max-w-4xl mx-auto">
		<div class="mb-8 flex justify-center">
			<img 
				src="pink.png" 
				alt="Peerless Logo" 
				class="h-32 w-32 filter drop-shadow-2xl {logo_loaded ? 'animate-bounce' : ''}"
			>
		</div>
		
		<h1 class="text-5xl md:text-7xl font-bold mb-6">
			<span class="bg-gradient-to-r from-pink-400 via-pink-500 to-purple-600 bg-clip-text text-transparent">
				Peerless
			</span>
		</h1>
		
		<p class="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
			The ultimate Discord bot for managing Roblox sports leagues with precision and style
		</p>
		
		<div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
			<a 
				href="https://discord.com/application-directory/1105640024113954829"
				class="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl"
			>
				<i class="fab fa-discord mr-2"></i>
				Add to Server
			</a>
			<a 
				href="#features"
				class="border-2 border-pink-500 text-pink-400 hover:bg-pink-500 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300"
			>
				Learn More
			</a>
		</div>
		
		<div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
			<div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
				<div class="text-3xl font-bold text-pink-400 mb-2">14,000+</div>
				<div class="text-gray-300">Servers</div>
			</div>
			<div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
				<div class="text-3xl font-bold text-pink-400 mb-2">1M+</div>
				<div class="text-gray-300">Users</div>
			</div>
			<div class="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-gray-700">
				<div class="text-3xl font-bold text-pink-400 mb-2">99.9%</div>
				<div class="text-gray-300">Uptime</div>
			</div>
		</div>
	</div>
</section>
