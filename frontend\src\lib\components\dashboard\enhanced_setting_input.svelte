<script lang="ts">
	let { setting, value, on_change, disabled = false, server_data = null, saving_state = false } = $props();

	function get_initial_value() {
		if (value !== undefined && value !== null) {
			return value;
		}

		if (setting.type === 'multichannel' || setting.type === 'multirole') {
			return Array.isArray(setting.default_value) ? setting.default_value : [];
		}

		return setting.default_value;
	}

	let input_value = $state(get_initial_value());
	let pending_value = $state(get_initial_value());
	let has_changes = $state(false);

	$effect(() => {
		const new_value = get_initial_value();
		input_value = new_value;
		pending_value = new_value;
		has_changes = false;
	});

	function handle_change(new_value: any) {
		pending_value = new_value;
		has_changes = pending_value !== input_value;
	}

	function save_changes() {
		input_value = pending_value;
		on_change(pending_value);
		has_changes = false;
	}

	function cancel_changes() {
		pending_value = input_value;
		has_changes = false;
	}

	function handle_input_change(event: Event) {
		const target = event.target as HTMLInputElement;
		let new_value: any = target.value;

		if (setting.type === 'number') {
			new_value = parseInt(new_value) || setting.default_value;
			if (setting.minimum !== undefined && new_value < setting.minimum) {
				new_value = setting.minimum;
			}
			if (setting.maximum !== undefined && new_value > setting.maximum) {
				new_value = setting.maximum;
			}
		}

		handle_change(new_value);
	}

	function handle_select_change(event: Event) {
		const target = event.target as HTMLSelectElement;
		handle_change(target.value);
	}

	function handle_boolean_toggle() {
		handle_change(!pending_value);
	}

	function get_available_options() {
		if (!server_data) return [];

		switch (setting.type) {
			case 'channel':
			case 'multichannel':
				return server_data.channels || [];
			case 'role':
			case 'multirole':
				return server_data.roles || [];
			case 'user':
			case 'multiuser':
				return server_data.members || [];
			default:
				return [];
		}
	}

	function add_selection(option_id: string) {
		if (!Array.isArray(pending_value)) {
			pending_value = [];
		}
		if (!pending_value.includes(option_id)) {
			handle_change([...pending_value, option_id]);
		}
	}

	function remove_selection(option_id: string) {
		if (Array.isArray(pending_value)) {
			handle_change(pending_value.filter(id => id !== option_id));
		}
	}

	function get_option_name(option_id: string) {
		const options = get_available_options();
		const option = options.find(opt => opt.id === option_id);
		return option ? option.name : option_id;
	}
</script>

<div class="group bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl border border-gray-700/50 p-6 hover:border-pink-500/30 transition-all duration-300 shadow-lg hover:shadow-xl">
	<div class="flex items-start justify-between mb-6">
		<div class="flex-1">
			<div class="flex items-center space-x-3 mb-3">
				{#if setting.icon}
					<div class="flex items-center justify-center w-8 h-8 rounded-lg bg-pink-500/10 border border-pink-500/20">
						<i class="{setting.icon} text-pink-400 text-sm"></i>
					</div>
				{/if}
				<div class="flex items-center space-x-3">
					<h3 class="text-xl font-bold text-white tracking-tight">{setting.name}</h3>
					{#if setting.required}
						<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-red-500/20 text-red-300 border border-red-500/30 shadow-sm">
							<i class="fas fa-asterisk text-xs mr-1"></i>
							Required
						</span>
					{/if}
				</div>
			</div>
			<p class="text-gray-400 text-sm leading-relaxed max-w-2xl">{setting.description}</p>
		</div>

		<div class="ml-6 flex items-center space-x-3">
			{#if saving_state === true}
				<div class="flex items-center space-x-2 px-3 py-2 bg-blue-500/10 border border-blue-500/20 rounded-lg">
					<div class="animate-spin rounded-full h-4 w-4 border-2 border-blue-400 border-t-transparent"></div>
					<span class="text-sm text-blue-400 font-medium">Saving...</span>
				</div>
			{:else if saving_state === 'error'}
				<div class="flex items-center space-x-2 px-3 py-2 bg-red-500/10 border border-red-500/20 rounded-lg">
					<i class="fas fa-exclamation-triangle text-red-400"></i>
					<span class="text-sm text-red-400 font-medium">Error</span>
				</div>
			{:else if value !== null}
				<div class="flex items-center space-x-2 px-3 py-2 bg-green-500/10 border border-green-500/20 rounded-lg">
					<i class="fas fa-check text-green-400"></i>
					<span class="text-sm text-green-400 font-medium">Saved</span>
				</div>
			{/if}
		</div>
	</div>

	<div class="bg-gray-800/50 backdrop-blur-sm rounded-xl p-5 border border-gray-600/30 shadow-inner">
		{#if setting.type === 'boolean'}
			<div class="flex items-center justify-between p-4 bg-gradient-to-r from-gray-700/30 to-gray-600/20 rounded-xl border border-gray-600/40">
				<div class="flex items-center space-x-4">
					<div class="flex items-center justify-center w-10 h-10 rounded-xl {pending_value ? 'bg-pink-500/20 border border-pink-500/30' : 'bg-gray-600/20 border border-gray-500/30'} transition-all duration-300">
						<i class="fas {pending_value ? 'fa-check' : 'fa-times'} {pending_value ? 'text-pink-400' : 'text-gray-400'} transition-all duration-300"></i>
					</div>
					<div>
						<div class="flex items-center space-x-2">
							<span class="text-lg font-bold {pending_value ? 'text-pink-400' : 'text-gray-400'} transition-colors duration-300">{pending_value ? 'ON' : 'OFF'}</span>
							<span class="text-xs px-2 py-1 rounded-full {pending_value ? 'bg-pink-500/20 text-pink-300 border border-pink-500/30' : 'bg-gray-600/20 text-gray-400 border border-gray-500/30'} transition-all duration-300">
								{pending_value ? 'Enabled' : 'Disabled'}
							</span>
						</div>
						<p class="text-xs text-gray-500 mt-1">Click to {pending_value ? 'disable' : 'enable'}</p>
					</div>
				</div>
				<button
					type="button"
					onclick={handle_boolean_toggle}
					{disabled}
					aria-label="Toggle {setting.name}"
					class="relative inline-flex h-8 w-16 items-center rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-pink-500/50 focus:ring-offset-2 focus:ring-offset-gray-800
						   {pending_value ? 'bg-gradient-to-r from-pink-500 to-pink-600 shadow-lg shadow-pink-500/30' : 'bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg shadow-gray-600/20'}
						   {disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-xl'}"
				>
					<span class="inline-block h-6 w-6 transform rounded-full bg-white transition-all duration-300 shadow-lg
								 {pending_value ? 'translate-x-9' : 'translate-x-1'}
								 {!disabled && 'hover:scale-110'}"></span>
				</button>
			</div>

		{:else if setting.type === 'channel'}
			<div class="space-y-3">
				<div class="relative group">
					<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
						<i class="fas fa-hashtag text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
					<select
						bind:value={pending_value}
						onchange={handle_select_change}
						{disabled}
						class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-12 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 appearance-none cursor-pointer shadow-inner hover:border-gray-500 focus:shadow-lg"
					>
						<option value="" class="bg-gray-800 text-gray-300">Select a channel...</option>
						{#each get_available_options() as option}
							<option value={option.id} class="bg-gray-800 text-white">#{option.name}</option>
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
						<i class="fas fa-chevron-down text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
				</div>
				{#if pending_value}
					<div class="flex items-center space-x-2 px-3 py-2 bg-green-500/10 border border-green-500/20 rounded-lg">
						<i class="fas fa-hashtag text-green-400 text-xs"></i>
						<span class="text-sm text-green-400 font-medium">{get_option_name(pending_value)}</span>
					</div>
				{/if}
			</div>

		{:else if setting.type === 'role'}
			<div class="space-y-3">
				<div class="relative group">
					<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
						<i class="fas fa-at text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
					<select
						bind:value={pending_value}
						onchange={handle_select_change}
						{disabled}
						class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-12 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 appearance-none cursor-pointer shadow-inner hover:border-gray-500 focus:shadow-lg"
					>
						<option value="" class="bg-gray-800 text-gray-300">Select a role...</option>
						{#each get_available_options() as option}
							<option value={option.id} class="bg-gray-800 text-white">@{option.name}</option>
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
						<i class="fas fa-chevron-down text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
				</div>
				{#if pending_value}
					<div class="flex items-center space-x-2 px-3 py-2 bg-blue-500/10 border border-blue-500/20 rounded-lg">
						<i class="fas fa-at text-blue-400 text-xs"></i>
						<span class="text-sm text-blue-400 font-medium">{get_option_name(pending_value)}</span>
					</div>
				{/if}
			</div>

		{:else if setting.type === 'multichannel'}
			<div class="space-y-4">
				{#if Array.isArray(pending_value) && pending_value.length > 0}
					<div class="space-y-3">
						<div class="flex items-center space-x-2">
							<i class="fas fa-hashtag text-green-400 text-sm"></i>
							<span class="text-sm font-medium text-green-400">Selected Channels ({pending_value.length})</span>
						</div>
						<div class="flex flex-wrap gap-2">
							{#each pending_value as channel_id}
								<div class="group inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-green-500/20 to-green-600/10 border border-green-500/30 text-green-300 rounded-xl text-sm font-medium hover:from-green-500/30 hover:to-green-600/20 transition-all duration-200">
									<i class="fas fa-hashtag text-xs"></i>
									<span>{get_option_name(channel_id)}</span>
									<button
										type="button"
										onclick={() => remove_selection(channel_id)}
										class="ml-1 text-green-400 hover:text-red-400 hover:bg-red-500/20 rounded-full p-1 transition-all duration-200 group-hover:scale-110"
										aria-label="Remove channel"
									>
										<i class="fas fa-times text-xs"></i>
									</button>
								</div>
							{/each}
						</div>
					</div>
				{/if}

				<div class="relative group">
					<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
						<i class="fas fa-plus text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
					<select
						onchange={(e) => {
							const target = e.target as HTMLSelectElement;
							if (target.value) {
								add_selection(target.value);
								target.value = '';
							}
						}}
						{disabled}
						class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-12 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 appearance-none cursor-pointer shadow-inner hover:border-gray-500 focus:shadow-lg"
					>
						<option value="" class="bg-gray-800 text-gray-300">Add a channel...</option>
						{#each get_available_options() as option}
							{#if !Array.isArray(pending_value) || !pending_value.includes(option.id)}
								<option value={option.id} class="bg-gray-800 text-white">#{option.name}</option>
							{/if}
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
						<i class="fas fa-chevron-down text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
				</div>
			</div>

		{:else if setting.type === 'multirole'}
			<div class="space-y-4">
				{#if Array.isArray(pending_value) && pending_value.length > 0}
					<div class="space-y-3">
						<div class="flex items-center space-x-2">
							<i class="fas fa-at text-blue-400 text-sm"></i>
							<span class="text-sm font-medium text-blue-400">Selected Roles ({pending_value.length})</span>
						</div>
						<div class="flex flex-wrap gap-2">
							{#each pending_value as role_id}
								<div class="group inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-500/20 to-blue-600/10 border border-blue-500/30 text-blue-300 rounded-xl text-sm font-medium hover:from-blue-500/30 hover:to-blue-600/20 transition-all duration-200">
									<i class="fas fa-at text-xs"></i>
									<span>{get_option_name(role_id)}</span>
									<button
										type="button"
										onclick={() => remove_selection(role_id)}
										class="ml-1 text-blue-400 hover:text-red-400 hover:bg-red-500/20 rounded-full p-1 transition-all duration-200 group-hover:scale-110"
										aria-label="Remove role"
									>
										<i class="fas fa-times text-xs"></i>
									</button>
								</div>
							{/each}
						</div>
					</div>
				{/if}

				<div class="relative group">
					<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
						<i class="fas fa-plus text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
					<select
						onchange={(e) => {
							const target = e.target as HTMLSelectElement;
							if (target.value) {
								add_selection(target.value);
								target.value = '';
							}
						}}
						{disabled}
						class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-12 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 appearance-none cursor-pointer shadow-inner hover:border-gray-500 focus:shadow-lg"
					>
						<option value="" class="bg-gray-800 text-gray-300">Add a role...</option>
						{#each get_available_options() as option}
							{#if !Array.isArray(pending_value) || !pending_value.includes(option.id)}
								<option value={option.id} class="bg-gray-800 text-white">@{option.name}</option>
							{/if}
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
						<i class="fas fa-chevron-down text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
				</div>
			</div>

		{:else if setting.type === 'number'}
			<div class="space-y-3">
				<div class="relative group">
					<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
						<i class="fas fa-hashtag text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
					</div>
					<input
						type="number"
						bind:value={pending_value}
						oninput={handle_input_change}
						{disabled}
						min={setting.minimum}
						max={setting.maximum}
						placeholder={setting.placeholder || 'Enter a number...'}
						class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-4 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 shadow-inner hover:border-gray-500 focus:shadow-lg"
					>
				</div>
				{#if setting.minimum !== undefined || setting.maximum !== undefined}
					<div class="flex justify-between items-center px-3 py-2 bg-gray-700/30 rounded-lg border border-gray-600/30">
						<div class="flex items-center space-x-2">
							<i class="fas fa-arrow-down text-orange-400 text-xs"></i>
							<span class="text-xs text-orange-400 font-medium">Min: {setting.minimum ?? 'None'}</span>
						</div>
						<div class="flex items-center space-x-2">
							<i class="fas fa-arrow-up text-orange-400 text-xs"></i>
							<span class="text-xs text-orange-400 font-medium">Max: {setting.maximum ?? 'None'}</span>
						</div>
					</div>
				{/if}
			</div>

		{:else if setting.type === 'select' || setting.type === 'theme' || setting.type === 'option'}
			<div class="relative group">
				<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
					<i class="fas fa-list text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
				</div>
				<select
					bind:value={pending_value}
					onchange={handle_select_change}
					{disabled}
					class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-12 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 appearance-none cursor-pointer shadow-inner hover:border-gray-500 focus:shadow-lg"
				>
					<option value="" class="bg-gray-800 text-gray-300">Select an option...</option>
					{#if setting.options}
						{#each setting.options as option}
							<option value={typeof option === 'object' ? option.name : option} class="bg-gray-800 text-white">
								{typeof option === 'object' ? option.description : option}
							</option>
						{/each}
					{/if}
				</select>
				<div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
					<i class="fas fa-chevron-down text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
				</div>
			</div>
		
		{:else if setting.type === 'day'}
			<div class="relative group">
				<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
					<i class="fas fa-calendar text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
				</div>
				<select
					bind:value={pending_value}
					onchange={handle_select_change}
					{disabled}
					class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-12 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 appearance-none cursor-pointer shadow-inner hover:border-gray-500 focus:shadow-lg"
				>
					<option value="" class="bg-gray-800 text-gray-300">Select day...</option>
					<option value="monday" class="bg-gray-800 text-white">Monday</option>
					<option value="tuesday" class="bg-gray-800 text-white">Tuesday</option>
					<option value="wednesday" class="bg-gray-800 text-white">Wednesday</option>
					<option value="thursday" class="bg-gray-800 text-white">Thursday</option>
					<option value="friday" class="bg-gray-800 text-white">Friday</option>
					<option value="saturday" class="bg-gray-800 text-white">Saturday</option>
					<option value="sunday" class="bg-gray-800 text-white">Sunday</option>
				</select>
				<div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
					<i class="fas fa-chevron-down text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
				</div>
			</div>

		{:else if setting.type === 'timezone'}
			<div class="relative group">
				<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
					<i class="fas fa-globe text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
				</div>
				<select
					bind:value={pending_value}
					onchange={handle_select_change}
					{disabled}
					class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-12 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 appearance-none cursor-pointer shadow-inner hover:border-gray-500 focus:shadow-lg"
				>
					<option value="" class="bg-gray-800 text-gray-300">Select timezone...</option>
					<option value="UTC" class="bg-gray-800 text-white">UTC</option>
					<option value="America/New_York" class="bg-gray-800 text-white">Eastern Time</option>
					<option value="America/Chicago" class="bg-gray-800 text-white">Central Time</option>
					<option value="America/Denver" class="bg-gray-800 text-white">Mountain Time</option>
					<option value="America/Los_Angeles" class="bg-gray-800 text-white">Pacific Time</option>
					<option value="Europe/London" class="bg-gray-800 text-white">London</option>
					<option value="Europe/Paris" class="bg-gray-800 text-white">Paris</option>
					<option value="Asia/Tokyo" class="bg-gray-800 text-white">Tokyo</option>
				</select>
				<div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
					<i class="fas fa-chevron-down text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
				</div>
			</div>

		{:else}
			<div class="relative group">
				<div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
					<i class="fas fa-edit text-gray-400 text-sm group-focus-within:text-pink-400 transition-colors duration-200"></i>
				</div>
				<input
					type="text"
					bind:value={pending_value}
					oninput={handle_input_change}
					{disabled}
					placeholder={setting.placeholder || 'Enter text...'}
					class="w-full bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-600/50 rounded-xl pl-10 pr-4 py-4 text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 focus:bg-gray-700 transition-all duration-300 disabled:opacity-50 shadow-inner hover:border-gray-500 focus:shadow-lg"
				>
			</div>
		{/if}
	</div>

	{#if has_changes}
		<div class="flex space-x-4 justify-end pt-4 border-t border-gray-700/50">
			<button
				type="button"
				onclick={cancel_changes}
				class="group flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-xl hover:from-gray-500 hover:to-gray-600 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500/50 focus:ring-offset-2 focus:ring-offset-gray-800 shadow-lg hover:shadow-xl"
			>
				<i class="fas fa-times text-sm group-hover:rotate-90 transition-transform duration-300"></i>
				<span class="font-medium">Cancel</span>
			</button>
			<button
				type="button"
				onclick={save_changes}
				disabled={saving_state === true}
				class="group flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-pink-500 to-pink-600 text-white rounded-xl hover:from-pink-400 hover:to-pink-500 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-pink-500/50 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-lg hover:shadow-xl hover:shadow-pink-500/25"
			>
				{#if saving_state === true}
					<div class="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
					<span class="font-medium">Saving...</span>
				{:else}
					<i class="fas fa-save text-sm group-hover:scale-110 transition-transform duration-300"></i>
					<span class="font-medium">Save Changes</span>
				{/if}
			</button>
		</div>
	{/if}
</div>
