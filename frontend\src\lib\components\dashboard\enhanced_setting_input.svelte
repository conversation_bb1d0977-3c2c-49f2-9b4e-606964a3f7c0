<script lang="ts">
	let { setting, value, on_change, disabled = false, server_data = null, saving_state = false } = $props();

	let input_value = $state(value ?? setting.default_value);
	let pending_value = $state(input_value);
	let has_changes = $state(false);

	$effect(() => {
		input_value = value ?? setting.default_value;
		pending_value = input_value;
		has_changes = false;
	});

	function handle_change(new_value: any) {
		pending_value = new_value;
		has_changes = pending_value !== input_value;
	}

	function save_changes() {
		input_value = pending_value;
		on_change(pending_value);
		has_changes = false;
	}

	function cancel_changes() {
		pending_value = input_value;
		has_changes = false;
	}

	function handle_input_change(event: Event) {
		const target = event.target as HTMLInputElement;
		let new_value: any = target.value;

		if (setting.type === 'number') {
			new_value = parseInt(new_value) || setting.default_value;
			if (setting.minimum !== undefined && new_value < setting.minimum) {
				new_value = setting.minimum;
			}
			if (setting.maximum !== undefined && new_value > setting.maximum) {
				new_value = setting.maximum;
			}
		}

		handle_change(new_value);
	}

	function handle_select_change(event: Event) {
		const target = event.target as HTMLSelectElement;
		handle_change(target.value);
	}

	function handle_boolean_toggle() {
		handle_change(!pending_value);
	}

	function get_available_options() {
		if (!server_data) return [];

		switch (setting.type) {
			case 'channel':
			case 'multichannel':
				return server_data.channels || [];
			case 'role':
			case 'multirole':
				return server_data.roles || [];
			case 'user':
			case 'multiuser':
				return server_data.members || [];
			default:
				return [];
		}
	}

	function add_selection(option_id: string) {
		if (!Array.isArray(pending_value)) {
			pending_value = [];
		}
		if (!pending_value.includes(option_id)) {
			handle_change([...pending_value, option_id]);
		}
	}

	function remove_selection(option_id: string) {
		if (Array.isArray(pending_value)) {
			handle_change(pending_value.filter(id => id !== option_id));
		}
	}

	function get_option_name(option_id: string) {
		const options = get_available_options();
		const option = options.find(opt => opt.id === option_id);
		return option ? option.name : option_id;
	}
</script>

<div class="bg-gray-900 rounded-xl border border-gray-700 p-6">
	<!-- Setting Header -->
	<div class="flex items-start justify-between mb-4">
		<div class="flex-1">
			<div class="flex items-center space-x-2 mb-2">
				{#if setting.icon}
					<i class="{setting.icon} text-pink-400"></i>
				{/if}
				<h3 class="text-lg font-semibold text-white">{setting.name}</h3>
				{#if setting.required}
					<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-500/20 text-red-400 border border-red-500/30">
						Required
					</span>
				{/if}
			</div>
			<p class="text-gray-300 text-sm">{setting.description}</p>
		</div>

		<div class="ml-4 flex items-center space-x-2">
			{#if saving_state === true}
				<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-pink-400"></div>
				<span class="text-sm text-gray-400">Saving...</span>
			{:else if saving_state === 'error'}
				<i class="fas fa-exclamation-triangle text-red-400"></i>
				<span class="text-sm text-red-400">Error</span>
			{:else if value !== null}
				<i class="fas fa-check text-green-400"></i>
				<span class="text-sm text-green-400">Saved</span>
			{/if}
		</div>
	</div>

	<!-- Setting Input -->
	<div class="bg-gray-800 rounded-lg p-4 border border-gray-700">
		{#if setting.type === 'boolean'}
			<!-- Toggle Switch for Boolean -->
			<div class="flex items-center justify-between">
				<span class="text-gray-300">
					{pending_value ? 'Enabled' : 'Disabled'}
				</span>
				<button
					type="button"
					onclick={handle_boolean_toggle}
					{disabled}
					class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-pink-400 focus:ring-offset-2 focus:ring-offset-gray-800 {pending_value ? 'bg-pink-500' : 'bg-gray-600'} {disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}"
					aria-label="Toggle {setting.name}"
				>
					<span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform {pending_value ? 'translate-x-6' : 'translate-x-1'}"></span>
				</button>
			</div>

		{:else if setting.type === 'channel'}
			<!-- Channel Selector with Dropdown -->
			<div class="space-y-2">
				<div class="relative">
					<select
						bind:value={pending_value}
						onchange={handle_select_change}
						{disabled}
						class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 pr-10 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50 appearance-none cursor-pointer"
					>
						<option value="">Select a channel...</option>
						{#each get_available_options() as option}
							<option value={option.id}>#{option.name}</option>
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
						<i class="fas fa-chevron-down text-gray-400 text-sm"></i>
					</div>
				</div>
			</div>

		{:else if setting.type === 'role'}
			<!-- Role Selector with Dropdown -->
			<div class="space-y-2">
				<div class="relative">
					<select
						bind:value={pending_value}
						onchange={handle_select_change}
						{disabled}
						class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 pr-10 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50 appearance-none cursor-pointer"
					>
						<option value="">Select a role...</option>
						{#each get_available_options() as option}
							<option value={option.id}>@{option.name}</option>
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
						<i class="fas fa-chevron-down text-gray-400 text-sm"></i>
					</div>
				</div>
			</div>

		{:else if setting.type === 'multichannel'}
			<!-- Multi-Channel Selector -->
			<div class="space-y-3">
				<!-- Selected Channels -->
				{#if Array.isArray(pending_value) && pending_value.length > 0}
					<div class="flex flex-wrap gap-2">
						{#each pending_value as channel_id}
							<span class="inline-flex items-center gap-2 px-3 py-1 bg-gray-700 text-white rounded-lg text-sm">
								#{get_option_name(channel_id)}
								<button
									type="button"
									onclick={() => remove_selection(channel_id)}
									class="text-gray-400 hover:text-red-400 transition-colors"
									aria-label="Remove channel"
								>
									<i class="fas fa-times text-xs"></i>
								</button>
							</span>
						{/each}
					</div>
				{/if}

				<!-- Add Channel Dropdown -->
				<div class="relative">
					<select
						onchange={(e) => {
							const target = e.target as HTMLSelectElement;
							if (target.value) {
								add_selection(target.value);
								target.value = '';
							}
						}}
						{disabled}
						class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 pr-10 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50 appearance-none cursor-pointer"
					>
						<option value="">Add a channel...</option>
						{#each get_available_options() as option}
							{#if !Array.isArray(pending_value) || !pending_value.includes(option.id)}
								<option value={option.id}>#{option.name}</option>
							{/if}
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
						<i class="fas fa-plus text-gray-400 text-sm"></i>
					</div>
				</div>
			</div>

		{:else if setting.type === 'multirole'}
			<!-- Multi-Role Selector -->
			<div class="space-y-3">
				<!-- Selected Roles -->
				{#if Array.isArray(pending_value) && pending_value.length > 0}
					<div class="flex flex-wrap gap-2">
						{#each pending_value as role_id}
							<span class="inline-flex items-center gap-2 px-3 py-1 bg-gray-700 text-white rounded-lg text-sm">
								@{get_option_name(role_id)}
								<button
									type="button"
									onclick={() => remove_selection(role_id)}
									class="text-gray-400 hover:text-red-400 transition-colors"
									aria-label="Remove role"
								>
									<i class="fas fa-times text-xs"></i>
								</button>
							</span>
						{/each}
					</div>
				{/if}

				<!-- Add Role Dropdown -->
				<div class="relative">
					<select
						onchange={(e) => {
							const target = e.target as HTMLSelectElement;
							if (target.value) {
								add_selection(target.value);
								target.value = '';
							}
						}}
						{disabled}
						class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 pr-10 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50 appearance-none cursor-pointer"
					>
						<option value="">Add a role...</option>
						{#each get_available_options() as option}
							{#if !Array.isArray(pending_value) || !pending_value.includes(option.id)}
								<option value={option.id}>@{option.name}</option>
							{/if}
						{/each}
					</select>
					<div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
						<i class="fas fa-plus text-gray-400 text-sm"></i>
					</div>
				</div>
			</div>

		{:else if setting.type === 'number'}
			<!-- Number Input with Range Display -->
			<div class="space-y-2">
				<input 
					type="number"
					bind:value={pending_value}
					oninput={handle_input_change}
					{disabled}
					min={setting.minimum}
					max={setting.maximum}
					placeholder={setting.placeholder || ''}
					class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
				>
				{#if setting.minimum !== undefined || setting.maximum !== undefined}
					<div class="flex justify-between text-xs text-gray-500">
						<span>Min: {setting.minimum ?? 'None'}</span>
						<span>Max: {setting.maximum ?? 'None'}</span>
					</div>
				{/if}
			</div>

		{:else if setting.type === 'select' || setting.type === 'theme' || setting.type === 'option'}
			<!-- Select Dropdown -->
			<select 
				bind:value={pending_value}
				onchange={handle_select_change}
				{disabled}
				class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
			>
				<option value="">Select an option...</option>
				{#if setting.options}
					{#each setting.options as option}
						<option value={typeof option === 'object' ? option.value : option}>
							{typeof option === 'object' ? option.label : option}
						</option>
					{/each}
				{/if}
			</select>

		{:else if setting.type === 'day'}
			<!-- Day Selector -->
			<select 
				bind:value={pending_value}
				onchange={handle_select_change}
				{disabled}
				class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
			>
				<option value="">Select day...</option>
				<option value="monday">Monday</option>
				<option value="tuesday">Tuesday</option>
				<option value="wednesday">Wednesday</option>
				<option value="thursday">Thursday</option>
				<option value="friday">Friday</option>
				<option value="saturday">Saturday</option>
				<option value="sunday">Sunday</option>
			</select>

		{:else if setting.type === 'timezone'}
			<!-- Timezone Selector -->
			<select 
				bind:value={pending_value}
				onchange={handle_select_change}
				{disabled}
				class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
			>
				<option value="">Select timezone...</option>
				<option value="UTC">UTC</option>
				<option value="America/New_York">Eastern Time</option>
				<option value="America/Chicago">Central Time</option>
				<option value="America/Denver">Mountain Time</option>
				<option value="America/Los_Angeles">Pacific Time</option>
				<option value="Europe/London">London</option>
				<option value="Europe/Paris">Paris</option>
				<option value="Asia/Tokyo">Tokyo</option>
			</select>

		{:else}
			<!-- Text Input -->
			<input 
				type="text"
				bind:value={pending_value}
				oninput={handle_input_change}
				{disabled}
				placeholder={setting.placeholder || ''}
				class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-3 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
			>
		{/if}
	</div>

	<!-- Save/Cancel Buttons -->
	{#if has_changes}
		<div class="flex space-x-3 justify-end">
			<button
				type="button"
				onclick={cancel_changes}
				class="px-4 py-2 text-gray-400 hover:text-white transition-colors"
			>
				Cancel
			</button>
			<button
				type="button"
				onclick={save_changes}
				class="px-6 py-2 bg-pink-500 hover:bg-pink-600 text-white rounded-lg transition-colors font-medium"
			>
				Save Changes
			</button>
		</div>
	{/if}
</div>
