<script lang="ts">
	export let server: {
		id: string;
		name: string;
		icon: string | null;
		owner: boolean;
		permissions: string;
		member_count?: number;
		bot_permissions?: string[];
	};

	function get_server_icon_url(server_id: string, icon_hash: string | null) {
		if (!icon_hash) {
			return `https://cdn.discordapp.com/embed/avatars/${parseInt(server_id) % 5}.png`;
		}
		return `https://cdn.discordapp.com/icons/${server_id}/${icon_hash}.png?size=128`;
	}

	function get_permission_level(permissions: string) {
		const perms = parseInt(permissions);
		if ((perms & 0x8) === 0x8) return 'Administrator';
		if ((perms & 0x20) === 0x20) return 'Manage Server';
		if ((perms & 0x10000000) === 0x10000000) return 'Manage Roles';
		return 'Member';
	}

	function get_permission_color(permissions: string) {
		const level = get_permission_level(permissions);
		switch (level) {
			case 'Administrator': return 'text-red-400';
			case 'Manage Server': return 'text-yellow-400';
			case 'Manage Roles': return 'text-blue-400';
			default: return 'text-gray-400';
		}
	}
</script>

<div class="bg-gray-900 rounded-xl border border-gray-700 hover:border-pink-500 transition-all duration-300 overflow-hidden group">
	<div class="p-6">
		<div class="flex items-center space-x-4 mb-4">
			<img 
				src={get_server_icon_url(server.id, server.icon)} 
				alt="{server.name} icon"
				class="w-16 h-16 rounded-full border-2 border-gray-600 group-hover:border-pink-400 transition-colors duration-300"
			>
			<div class="flex-1 min-w-0">
				<h3 class="text-lg font-semibold text-white truncate group-hover:text-pink-400 transition-colors duration-300">
					{server.name}
				</h3>
				<div class="flex items-center space-x-2 mt-1">
					{#if server.owner}
						<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
							<i class="fas fa-crown mr-1"></i>
							Owner
						</span>
					{/if}
					<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 {get_permission_color(server.permissions)}">
						<i class="fas fa-shield-alt mr-1"></i>
						{get_permission_level(server.permissions)}
					</span>
				</div>
			</div>
		</div>

		{#if server.member_count}
			<div class="flex items-center text-gray-400 text-sm mb-4">
				<i class="fas fa-users mr-2"></i>
				{server.member_count.toLocaleString()} members
			</div>
		{/if}

		{#if server.bot_permissions && server.bot_permissions.length > 0}
			<div class="mb-4">
				<p class="text-sm text-gray-400 mb-2">Bot Permissions:</p>
				<div class="flex flex-wrap gap-1">
					{#each server.bot_permissions.slice(0, 3) as permission}
						<span class="inline-flex items-center px-2 py-1 rounded text-xs bg-pink-500/20 text-pink-300 border border-pink-500/30">
							{permission}
						</span>
					{/each}
					{#if server.bot_permissions.length > 3}
						<span class="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-700 text-gray-300">
							+{server.bot_permissions.length - 3} more
						</span>
					{/if}
				</div>
			</div>
		{/if}

		<div class="flex space-x-2">
			<a 
				href="/dashboard/{server.id}"
				class="flex-1 bg-pink-500 hover:bg-pink-600 text-white text-center py-2 px-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
			>
				<i class="fas fa-cog mr-2"></i>
				Manage
			</a>
			<button 
				class="bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white py-2 px-4 rounded-lg transition-all duration-300"
				title="Server Info"
			>
				<i class="fas fa-info-circle"></i>
			</button>
		</div>
	</div>
</div>
