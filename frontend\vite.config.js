import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
	plugins: [sveltekit(), tailwindcss()],
	server: {
		host: true,
		strictPort: true,
		watch: {
			usePolling: true,
		},
		proxy: {
			'/account': {
				target: 'http://api:8000',
				changeOrigin: true,
			},
			'/api': {
				target: 'http://api:8000',
				changeOrigin: true,
			},
		},
	},
});
