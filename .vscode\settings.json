{
    "[python]": {
        "diffEditor.ignoreTrimWhitespace": false,
        "editor.defaultColorDecorators": "never",
        "editor.formatOnType": true,
        "editor.wordBasedSuggestions": "off",

        "editor.defaultFormatter": "ms-python.isort",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "always"
        },
    },
    "isort.args": [
        "--profile",
        "black"
    ],
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.diagnosticSeverityOverrides": {
        "reportUnusedImport": "error",
        "reportUnnecessaryTypeIgnoreComment": "warning",
        "reportPossiblyUnboundVariable": "error"
    },
}