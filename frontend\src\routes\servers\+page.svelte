<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import Header from '$lib/components/home/<USER>';
	import ServerCard from '$lib/components/servers/server_card.svelte';

	let authenticated = $state(false);
	let user = $state({
		username: '',
		discord_id: '',
		avatar_url: ''
	});
	let servers = $state([]);
	let loading = $state(true);
	let error_message = $state('');

	onMount(async () => {
		if (browser) {
			const saved_user = localStorage.getItem('user');
			if (saved_user) {
				try {
					const local_user = JSON.parse(saved_user);
					user = local_user;
					authenticated = true;
					await load_mutual_servers();
				} catch (e) {
					console.error('Failed to parse user data:', e);
					localStorage.removeItem('user');
					loading = false;
				}
			} else {
				loading = false;
			}
		}
	});

	async function load_mutual_servers() {
		try {
			const response = await fetch(`/api/servers/mutual/${user.discord_id}`);
			if (response.ok) {
				const data = await response.json();
				if (data.error) {
					error_message = data.error;
				} else {
					servers = data.servers || [];
				}
			} else {
				error_message = 'Failed to load servers';
			}
		} catch (e) {
			console.error('Failed to load mutual servers:', e);
			error_message = 'Failed to connect to server';
		} finally {
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>My Servers - Peerless Dashboard</title>
	<meta name="description" content="Manage your Discord servers with Peerless bot" />
</svelte:head>

<div class="bg-gray-800 text-gray-200 min-h-screen">
	<Header/>

	<main class="max-w-7xl mx-auto px-4 py-8">
		{#if !authenticated}
			<div class="text-center py-20">
				<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700">
					<i class="fas fa-lock text-pink-400 text-4xl mb-4"></i>
					<h2 class="text-2xl font-bold text-white mb-4">Authentication Required</h2>
					<p class="text-gray-300 mb-6">Please log in to view your servers</p>
					<a href="/account/login/discord" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
						<i class="fab fa-discord mr-2"></i>
						Login with Discord
					</a>
				</div>
			</div>
		{:else if loading}
			<div class="text-center py-20">
				<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700">
					<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-400 mx-auto mb-4"></div>
					<h2 class="text-xl font-semibold text-white">Loading your servers...</h2>
				</div>
			</div>
		{:else if error_message}
			<div class="text-center py-20">
				<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-red-500">
					<i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
					<h2 class="text-2xl font-bold text-white mb-4">Error</h2>
					<p class="text-gray-300 mb-6">{error_message}</p>
					<button onclick={() => window.location.reload()} class="bg-pink-500 hover:bg-pink-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
						Try Again
					</button>
				</div>
			</div>
		{:else}
			<div class="mb-8">
				<h1 class="text-4xl font-bold text-white mb-2">My Servers</h1>
				<p class="text-gray-300">Manage your Discord servers where Peerless is installed</p>
			</div>

			{#if servers.length === 0}
				<div class="text-center py-20">
					<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700">
						<i class="fas fa-server text-gray-400 text-4xl mb-4"></i>
						<h2 class="text-2xl font-bold text-white mb-4">No Servers Found</h2>
						<p class="text-gray-300 mb-6">You don't have any mutual servers with Peerless bot yet</p>
						<a href="https://discord.com/application-directory/1105640024113954829" class="bg-pink-500 hover:bg-pink-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
							<i class="fab fa-discord mr-2"></i>
							Invite Peerless
						</a>
					</div>
				</div>
			{:else}
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{#each servers as server}
						<ServerCard {server} />
					{/each}
				</div>
			{/if}
		{/if}
	</main>
</div>
