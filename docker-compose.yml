services:
    bot:
        init: true
        profiles:
            - main
        build:
            context: .
            dockerfile: Dockerfile.bot
        depends_on:
            - db
            - redis
        environment:
            - TOKEN=${TOKEN}
            - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:${POSTGRES_PORT}/${POSTGRES_DB}
            - REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}
        volumes:
            - .:/bot

    api:
        init: true
        profiles:
            - main
            - website
        build:
            context: .
            dockerfile: Dockerfile.api
        depends_on:
            - db
            - redis
        environment:
            - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:${POSTGRES_PORT}/${POSTGRES_DB}
            - REDIS_URL=redis://${REDIS_HOST}:${REDIS_PORT}
            - DISCORD_CLIENT_SECRET=${DISCORD_CLIENT_SECRET}
            - TOKEN=${TOKEN}
            - FRONTEND_HOST=localhost:5173
            - API_HOST=localhost:8000
            - WATCHFILES_FORCE_POLLING=true
        ports:
            - "8000:8000"
        volumes:
            - .:/backend

    frontend:
        init: true
        profiles:
            - main
            - website
        build:
            context: ./frontend
            dockerfile: Dockerfile.frontend
        ports:
            - "5173:5173"
        volumes:
            - ./frontend:/frontend
            - frontend_node_modules:/frontend/node_modules

    db:
        image: postgres:17.5
        restart: always
        environment:
            POSTGRES_USER: ${POSTGRES_USER}
            POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
            POSTGRES_DB: ${POSTGRES_DB}
        volumes:
            - pgdata:/var/lib/postgresql/data

    redis:
        image: redis:8.0.2
        restart: always

volumes:
    pgdata:
    frontend_node_modules: