<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { page } from '$app/stores';
	import Header from '$lib/components/home/<USER>';
	import DashboardSidebar from '$lib/components/dashboard/sidebar.svelte';
	import SettingsPanel from '$lib/components/dashboard/settings_panel.svelte';

	let authenticated = $state(false);
	let user = $state({
		username: '',
		discord_id: '',
		avatar_url: ''
	});
	let server = $state(null);
	let loading = $state(true);
	let error_message = $state('');
	let selected_category = $state('customization');
	let server_settings = $state({});

	$: server_id = $page.params.server_id;

	onMount(async () => {
		if (browser) {
			const saved_user = localStorage.getItem('user');
			if (saved_user) {
				try {
					const local_user = JSON.parse(saved_user);
					user = local_user;
					authenticated = true;
					await load_server_data();
				} catch (e) {
					console.error('Failed to parse user data:', e);
					localStorage.removeItem('user');
					loading = false;
				}
			} else {
				loading = false;
			}
		}
	});

	async function load_server_data() {
		try {
			const [server_response, settings_response] = await Promise.all([
				fetch(`/api/servers/${server_id}`),
				fetch(`/api/servers/${server_id}/settings`)
			]);

			if (server_response.ok) {
				const server_data = await server_response.json();
				if (server_data.error) {
					error_message = server_data.error;
				} else {
					server = server_data;
				}
			} else {
				error_message = 'Failed to load server data';
			}

			if (settings_response.ok) {
				const settings_data = await settings_response.json();
				if (!settings_data.error) {
					server_settings = settings_data.settings || {};
				}
			}
		} catch (e) {
			console.error('Failed to load server data:', e);
			error_message = 'Failed to connect to server';
		} finally {
			loading = false;
		}
	}

	async function save_setting(category_key: string, setting_key: string, value: any) {
		try {
			const response = await fetch(`/api/servers/${server_id}/settings`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					category: category_key,
					setting: setting_key,
					value: value,
					user_id: user.discord_id
				})
			});

			if (response.ok) {
				const result = await response.json();
				if (!result.error) {
					if (!server_settings[category_key]) {
						server_settings[category_key] = {};
					}
					server_settings[category_key][setting_key] = value;
					server_settings = { ...server_settings };
					return { success: true };
				} else {
					return { success: false, error: result.error };
				}
			} else {
				return { success: false, error: 'Failed to save setting' };
			}
		} catch (e) {
			console.error('Failed to save setting:', e);
			return { success: false, error: 'Network error' };
		}
	}

	function handle_category_change(category: string) {
		selected_category = category;
	}
</script>

<svelte:head>
	<title>{server ? `${server.name} Dashboard` : 'Dashboard'} - Peerless</title>
	<meta name="description" content="Manage your Discord server settings with Peerless bot" />
</svelte:head>

<div class="bg-gray-800 text-gray-200 min-h-screen">
	<Header/>

	{#if !authenticated}
		<div class="flex items-center justify-center min-h-[80vh]">
			<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700 text-center">
				<i class="fas fa-lock text-pink-400 text-4xl mb-4"></i>
				<h2 class="text-2xl font-bold text-white mb-4">Authentication Required</h2>
				<p class="text-gray-300 mb-6">Please log in to access the dashboard</p>
				<a href="/account/login/discord" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105">
					<i class="fab fa-discord mr-2"></i>
					Login with Discord
				</a>
			</div>
		</div>
	{:else if loading}
		<div class="flex items-center justify-center min-h-[80vh]">
			<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-gray-700 text-center">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-400 mx-auto mb-4"></div>
				<h2 class="text-xl font-semibold text-white">Loading dashboard...</h2>
			</div>
		</div>
	{:else if error_message}
		<div class="flex items-center justify-center min-h-[80vh]">
			<div class="bg-gray-900 rounded-xl p-8 max-w-md mx-auto border border-red-500 text-center">
				<i class="fas fa-exclamation-triangle text-red-400 text-4xl mb-4"></i>
				<h2 class="text-2xl font-bold text-white mb-4">Error</h2>
				<p class="text-gray-300 mb-6">{error_message}</p>
				<button onclick={() => window.location.reload()} class="bg-pink-500 hover:bg-pink-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300">
					Try Again
				</button>
			</div>
		</div>
	{:else if server}
		<div class="flex">
			<DashboardSidebar 
				{server} 
				{selected_category} 
				on_category_change={handle_category_change}
			/>
			<main class="flex-1 ml-64">
				<SettingsPanel 
					{server}
					{selected_category}
					{server_settings}
					{save_setting}
				/>
			</main>
		</div>
	{/if}
</div>
