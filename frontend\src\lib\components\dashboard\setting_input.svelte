<script lang="ts">
	let { setting, value, on_change, disabled = false, server_data = null } = $props();

	let input_value = $state(value ?? setting.default_value);
	let pending_value = $state(input_value);
	let has_changes = $state(false);

	$effect(() => {
		input_value = value ?? setting.default_value;
		pending_value = input_value;
		has_changes = false;
	});

	function handle_change(new_value: any) {
		pending_value = new_value;
		has_changes = pending_value !== input_value;
	}

	function save_changes() {
		input_value = pending_value;
		on_change(pending_value);
		has_changes = false;
	}

	function cancel_changes() {
		pending_value = input_value;
		has_changes = false;
	}

	function handle_input_change(event: Event) {
		const target = event.target as HTMLInputElement;
		let new_value: any = target.value;

		if (setting.type === 'number') {
			new_value = parseInt(new_value) || setting.default_value;
			if (setting.minimum !== undefined && new_value < setting.minimum) {
				new_value = setting.minimum;
			}
			if (setting.maximum !== undefined && new_value > setting.maximum) {
				new_value = setting.maximum;
			}
		}

		handle_change(new_value);
	}

	function handle_select_change(event: Event) {
		const target = event.target as HTMLSelectElement;
		handle_change(target.value);
	}

	function handle_boolean_toggle() {
		handle_change(!pending_value);
	}

	function get_available_options() {
		if (!server_data) return [];

		switch (setting.type) {
			case 'channel':
				return server_data.channels || [];
			case 'role':
				return server_data.roles || [];
			case 'user':
				return server_data.members || [];
			default:
				return [];
		}
	}
</script>

<div class="mt-4">
	{#if setting.type === 'theme'}
		<select 
			bind:value={input_value}
			onchange={handle_select_change}
			{disabled}
			class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
		>
			<option value="">Select theme...</option>
			<option value="default">Default</option>
			<option value="dark">Dark</option>
			<option value="light">Light</option>
			<option value="custom">Custom</option>
		</select>

	{:else if setting.type === 'channel'}
		<div class="flex items-center space-x-2">
			<input 
				type="text"
				bind:value={input_value}
				oninput={handle_input_change}
				placeholder="Channel ID or #channel-name"
				{disabled}
				class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
			>
			<button 
				type="button"
				{disabled}
				class="bg-pink-500 hover:bg-pink-600 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors"
				title="Browse channels"
			>
				<i class="fas fa-hashtag"></i>
			</button>
		</div>

	{:else if setting.type === 'role'}
		<div class="flex items-center space-x-2">
			<input 
				type="text"
				bind:value={input_value}
				oninput={handle_input_change}
				placeholder="Role ID or @role-name"
				{disabled}
				class="flex-1 bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
			>
			<button 
				type="button"
				{disabled}
				class="bg-pink-500 hover:bg-pink-600 disabled:opacity-50 text-white px-4 py-2 rounded-lg transition-colors"
				title="Browse roles"
			>
				<i class="fas fa-at"></i>
			</button>
		</div>

	{:else if setting.type === 'status'}
		<label class="flex items-center space-x-3 cursor-pointer">
			<input 
				type="checkbox"
				checked={input_value}
				onchange={handle_checkbox_change}
				{disabled}
				class="w-5 h-5 text-pink-500 bg-gray-800 border-gray-600 rounded focus:ring-pink-400 focus:ring-2 disabled:opacity-50"
			>
			<span class="text-gray-300">
				{input_value ? 'Enabled' : 'Disabled'}
			</span>
		</label>

	{:else if setting.type === 'number'}
		<input 
			type="number"
			bind:value={input_value}
			oninput={handle_input_change}
			min={setting.minimum}
			max={setting.maximum}
			{disabled}
			class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
		>

	{:else if setting.type === 'option' && setting.options}
		<select 
			bind:value={input_value}
			onchange={handle_select_change}
			{disabled}
			class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
		>
			<option value="">Select option...</option>
			{#each setting.options as option}
				<option value={option}>{option}</option>
			{/each}
		</select>

	{:else if setting.type === 'day'}
		<select 
			bind:value={input_value}
			onchange={handle_select_change}
			{disabled}
			class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
		>
			<option value="">Select day...</option>
			<option value="monday">Monday</option>
			<option value="tuesday">Tuesday</option>
			<option value="wednesday">Wednesday</option>
			<option value="thursday">Thursday</option>
			<option value="friday">Friday</option>
			<option value="saturday">Saturday</option>
			<option value="sunday">Sunday</option>
		</select>

	{:else if setting.type === 'timezone'}
		<select 
			bind:value={input_value}
			onchange={handle_select_change}
			{disabled}
			class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
		>
			<option value="">Select timezone...</option>
			<option value="UTC">UTC</option>
			<option value="America/New_York">Eastern Time</option>
			<option value="America/Chicago">Central Time</option>
			<option value="America/Denver">Mountain Time</option>
			<option value="America/Los_Angeles">Pacific Time</option>
			<option value="Europe/London">London</option>
			<option value="Europe/Paris">Paris</option>
			<option value="Asia/Tokyo">Tokyo</option>
		</select>

	{:else}
		<input 
			type="text"
			bind:value={input_value}
			oninput={handle_input_change}
			{disabled}
			class="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white focus:border-pink-400 focus:ring-1 focus:ring-pink-400 transition-colors disabled:opacity-50"
		>
	{/if}

	{#if setting.minimum !== undefined || setting.maximum !== undefined}
		<div class="mt-2 text-xs text-gray-400">
			{#if setting.minimum !== undefined && setting.maximum !== undefined}
				Range: {setting.minimum} - {setting.maximum}
			{:else if setting.minimum !== undefined}
				Minimum: {setting.minimum}
			{:else if setting.maximum !== undefined}
				Maximum: {setting.maximum}
			{/if}
		</div>
	{/if}
</div>
