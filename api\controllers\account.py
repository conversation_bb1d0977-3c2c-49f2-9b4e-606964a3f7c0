from typing import Dict
import os
import urllib.parse

import aiohttp
from litestar import get
from litestar.controller import Controller
from litestar.response import Redirect

client_id = os.environ.get("DISCORD_CLIENT_ID", "1280906689016758282")
client_secret = os.environ.get("DISCORD_CLIENT_SECRET", "dIMLHnDcfdptlhP57Wcjbp2hIV6Y56Uk")
redirect_uri_base = os.getenv("REDIRECT_URI", "http://localhost:5173")
redirect_uri = urllib.parse.quote_plus(f"{redirect_uri_base}/account/callback/discord")

class AccountController(Controller):
    path = "/account"
    
    @get("/login/discord")
    async def login_discord(self) -> Redirect:
        print("login_discord route called")
        discord_url = f"https://discord.com/api/oauth2/authorize?client_id={client_id}&redirect_uri={redirect_uri}&response_type=code&scope=identify"
        return Redirect(path=discord_url)
    
    @get("/callback/discord")
    async def callback_discord(self, code: str) -> Dict[str, str]:
        print("callback_discord route called")
        token_url = "https://discord.com/api/oauth2/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": f"{redirect_uri_base}/account/callback/discord",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, headers=headers, data=data) as response:
                token_data = await response.json()

        access_token = token_data.get("access_token")

        if not access_token:
            return {"error": "Failed to retrieve access token"}

        user_url = "https://discord.com/api/users/@me"
        headers = {"Authorization": f"Bearer {access_token}"}

        async with aiohttp.ClientSession() as session:
            async with session.get(user_url, headers=headers) as response:
                user_data = await response.json()

        username = user_data.get("username")
        discord_id = user_data.get("id")

        return {"message": f"Logged in as {username} with Discord ID {discord_id}"}

    @get("/logout")
    async def logout(self) -> Dict[str, str]:
        return {"__template__": "<html><head><meta http-equiv='refresh' content='0;url=/'></head><body>Redirecting to home...</body></html>"}
