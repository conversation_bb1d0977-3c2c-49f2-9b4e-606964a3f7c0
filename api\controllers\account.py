import os
import urllib.parse

import aiohttp
from litestar import get
from litestar.controller import Controller
from litestar.response import Redirect

client_id = os.environ.get("DISCORD_CLIENT_ID", "1280906689016758282")
client_secret = os.environ.get("DISCORD_CLIENT_SECRET", "dIMLHnDcfdptlhP57Wcjbp2hIV6Y56Uk")
redirect_uri_base = os.getenv("REDIRECT_URI", "http://localhost:5173")
redirect_uri = urllib.parse.quote_plus(f"{redirect_uri_base}/account/callback/discord")

class AccountController(Controller):
    path = "/account"
    
    @get("/login/discord")
    async def login_discord(self) -> Redirect:
        print("login_discord route called")
        discord_url = f"https://discord.com/api/oauth2/authorize?client_id={client_id}&redirect_uri={redirect_uri}&response_type=code&scope=identify"
        return Redirect(path=discord_url)
    
    @get("/callback/discord")
    async def callback_discord(self, code: str) -> Redirect:
        print("callback_discord route called")
        token_url = "https://discord.com/api/oauth2/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "client_id": client_id,
            "client_secret": client_secret,
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": f"{redirect_uri_base}/account/callback/discord",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(token_url, headers=headers, data=data) as response:
                token_data = await response.json()

        access_token = token_data.get("access_token")

        if not access_token:
            error_url = f"{redirect_uri_base}/?error=Failed to retrieve access token"
            return Redirect(path=error_url)

        user_url = "https://discord.com/api/users/@me"
        headers = {"Authorization": f"Bearer {access_token}"}

        async with aiohttp.ClientSession() as session:
            async with session.get(user_url, headers=headers) as response:
                user_data = await response.json()

        username = user_data.get("username")
        discord_id = user_data.get("id")
        avatar = user_data.get("avatar")

        avatar_url = None
        if avatar:
            avatar_url = f"https://cdn.discordapp.com/avatars/{discord_id}/{avatar}.png"
        else:
            default_avatar = int(discord_id) % 5
            avatar_url = f"https://cdn.discordapp.com/embed/avatars/{default_avatar}.png"

        redirect_url = f"{redirect_uri_base}/?username={urllib.parse.quote(username)}&discord_id={discord_id}&avatar_url={urllib.parse.quote(avatar_url)}"
        return Redirect(path=redirect_url)

    @get("/logout")
    async def logout(self) -> Redirect:
        return Redirect(path=redirect_uri_base)
