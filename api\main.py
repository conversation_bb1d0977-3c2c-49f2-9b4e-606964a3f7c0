from pathlib import Path
from litestar import Litestar
from litestar.config.cors import CORSConfig
from litestar.config import TemplateConfig
from litestar.template import Jinja2TemplateEngine

from api.controllers import account

def create_app() -> Litestar:
    template_engine = Jinja2TemplateEngine(
        directory=Path(__file__).parent / "templates",
    )
    
    return Litestar(
        route_handlers=[account.AccountController],
        template_config=TemplateConfig(
            engine=template_engine,
            enabled=True,
            default_context={"title": "Peerless"}
        ),
        cors_config=CORSConfig(
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            expose_headers=["*"]
        )
    )