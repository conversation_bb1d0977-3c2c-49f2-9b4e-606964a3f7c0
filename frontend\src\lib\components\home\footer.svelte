<script lang="ts">
	const footer_links = {
		product: [
			{ name: 'Features', href: '#features' },
			{ name: 'Pricing', href: '#pricing' },
			{ name: 'Documentation', href: '#' },
			{ name: 'API', href: '#' }
		],
		support: [
			{ name: 'Discord Server', href: 'https://discord.gg/FsNK9y7MPg' },
			{ name: 'Help Center', href: '#' },
			{ name: 'Status Page', href: '#' },
			{ name: 'Contact Us', href: '#' }
		],
		legal: [
			{ name: 'Privacy Policy', href: '#' },
			{ name: 'Terms of Service', href: '#' },
			{ name: 'Cookie Policy', href: '#' }
		]
	};
</script>

<footer class="bg-gray-900 border-t border-gray-800">
	<div class="max-w-6xl mx-auto px-4 py-12">
		<div class="grid grid-cols-1 md:grid-cols-4 gap-8">
			<div class="col-span-1 md:col-span-1">
				<div class="flex items-center mb-4">
					<img src="pink.png" alt="Peerless Logo" class="h-8 w-8 mr-3">
					<span class="text-xl font-bold bg-gradient-to-r from-pink-400 to-pink-600 bg-clip-text text-transparent">Peerless</span>
				</div>
				<p class="text-gray-400 mb-4">
					The ultimate Discord bot for managing Roblox sports leagues with precision and style.
				</p>
				<div class="flex space-x-4">
					<a href="https://discord.gg/FsNK9y7MPg" class="text-gray-400 hover:text-pink-400 transition-colors">
						<i class="fab fa-discord text-xl"></i>
					</a>
					<a href="#" class="text-gray-400 hover:text-pink-400 transition-colors">
						<i class="fab fa-twitter text-xl"></i>
					</a>
					<a href="#" class="text-gray-400 hover:text-pink-400 transition-colors">
						<i class="fab fa-github text-xl"></i>
					</a>
				</div>
			</div>
			
			<div>
				<h3 class="text-white font-semibold mb-4">Product</h3>
				<ul class="space-y-2">
					{#each footer_links.product as link}
						<li>
							<a href={link.href} class="text-gray-400 hover:text-pink-400 transition-colors">
								{link.name}
							</a>
						</li>
					{/each}
				</ul>
			</div>
			
			<div>
				<h3 class="text-white font-semibold mb-4">Support</h3>
				<ul class="space-y-2">
					{#each footer_links.support as link}
						<li>
							<a href={link.href} class="text-gray-400 hover:text-pink-400 transition-colors">
								{link.name}
							</a>
						</li>
					{/each}
				</ul>
			</div>
			
			<div>
				<h3 class="text-white font-semibold mb-4">Legal</h3>
				<ul class="space-y-2">
					{#each footer_links.legal as link}
						<li>
							<a href={link.href} class="text-gray-400 hover:text-pink-400 transition-colors">
								{link.name}
							</a>
						</li>
					{/each}
				</ul>
			</div>
		</div>
		
		<div class="border-t border-gray-800 mt-8 pt-8 text-center">
			<p class="text-gray-400">
				© 2025 Peerless. All rights reserved.
			</p>
		</div>
	</div>
</footer>
